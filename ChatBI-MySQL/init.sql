-- 初始化ChatBI数据库
USE chatbi;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `email` varchar(128) NOT NULL,
  `user_id` varchar(64) NOT NULL,
  `job_title` varchar(128) DEFAULT NULL,
  `open_id` varchar(64) NOT NULL,
  `union_id` varchar(64) DEFAULT NULL COMMENT '飞书用户union_id，用于获取API token',
  `last_login_time` datetime NOT NULL,
  `access_token` text,
  `refresh_token` text,
  `token_expires_at` datetime DEFAULT NULL,
  `avatar` text COMMENT '用户头像URL',
  `feishu_docs_folder_token` varchar(255) DEFAULT NULL,
  `feishu_docs_folder_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_user_openid` (`open_id`),
  KEY `idx_union_id` (`union_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1450 DEFAULT CHARSET=utf8mb4

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS `user_session` (
  `session_id` varchar(128) NOT NULL COMMENT '自生成的session ID',
  `open_id` varchar(64) NOT NULL COMMENT '飞书用户open_id',
  `refresh_token` text NOT NULL COMMENT '飞书refresh token',
  `access_token` text COMMENT '当前access token',
  `access_token_expires_at` datetime DEFAULT NULL COMMENT 'access token过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_active_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否活跃：1-活跃，0-已失效',
  PRIMARY KEY (`session_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_last_active` (`last_active_at`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户session表，存储refresh token实现长久登录'

-- 创建对话历史表
CREATE TABLE IF NOT EXISTS `chat_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(32) NOT NULL,
  `email` varchar(64) NOT NULL,
  `conversation_id` varchar(128) NOT NULL,
  `role` varchar(10) NOT NULL,
  `content` longtext,
  `logs` longtext,
  `output_as_input` text COMMENT '将AI的回复(role=assistant)作为下一轮会话的输入',
  `timestamp` bigint(20) NOT NULL COMMENT 'Millisecond timestamp',
  `is_bad_case` tinyint(4) DEFAULT '0' COMMENT 'Whether it is a bad case 0=No, 1=Yes',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record update time',
  `agent` varchar(100) DEFAULT NULL,
  `resource_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '资源URL，支持多个URL用逗号分隔，如图片、文档等',
  PRIMARY KEY (`id`),
  KEY `idx_user_convo_time` (`username`,`email`,`conversation_id`,`timestamp`),
  KEY `idx_convo_id` (`conversation_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_chat_history_agent` (`agent`)
) ENGINE=InnoDB AUTO_INCREMENT=4138 DEFAULT CHARSET=utf8mb4

CREATE TABLE IF NOT EXISTS `bad_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `conversation_id` varchar(128) NOT NULL,
  `repair_status` tinyint(4) DEFAULT '0' COMMENT '修复状态: 0=未修复, 1=已修复, 2=暂不修复',
  `marked_by` varchar(32) DEFAULT NULL COMMENT '标记人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标记时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `repair_note` text COMMENT '修复说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conversation_id` (`conversation_id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_repair_status` (`repair_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4

CREATE TABLE IF NOT EXISTS `share_map` (
  `share_id` varchar(128) NOT NULL,
  `conversation_id` varchar(128) NOT NULL,
  `owner_username` varchar(32) NOT NULL,
  `owner_email` varchar(64) NOT NULL,
  `created_at` bigint(20) NOT NULL COMMENT 'Millisecond timestamp of share creation',
  `db_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
  PRIMARY KEY (`share_id`),
  KEY `idx_owner` (`owner_username`,`owner_email`),
  KEY `idx_conversation_id` (`conversation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

CREATE TABLE IF NOT EXISTS `good_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `conversation_id` varchar(128) NOT NULL,
  `marked_by` varchar(32) DEFAULT NULL COMMENT '标记人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标记时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conversation_id` (`conversation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4